package com.yelink.dfscommon.dto.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 下推状态查询对象
 * 封装下推状态和目标单据类型的组合查询条件
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("下推状态查询对象")
public class PushDownQueryDTO {

    /**
     * 下推状态
     * noPushDown-未下推
     * partPushDown-部分下推
     * allPushDown-已下推
     */
    @ApiModelProperty("下推状态(noPushDown-未下推 partPushDown-部分下推 allPushDown-已下推)")
    private String pushDownState;

    /**
     * 目标单据类型
     * 配合下推状态使用，指定下推的目标单据类型
     */
    @ApiModelProperty("目标单据类型，配合下推状态使用")
    private String targetOrderType;

    /**
     * 检查是否有下推相关的查询条件
     *
     * @return true-有下推查询条件，false-无下推查询条件
     */
    public boolean hasPushDownQuery() {
        return pushDownState != null || targetOrderType != null;
    }

    /**
     * 检查是否为完整的下推查询条件（同时包含下推状态和目标单据类型）
     *
     * @return true-完整的下推查询条件，false-不完整
     */
    public boolean isCompletePushDownQuery() {
        return pushDownState != null && targetOrderType != null;
    }

    /**
     * 检查是否为未下推状态查询
     *
     * @return true-未下推状态查询，false-其他状态
     */
    public boolean isNoPushDownQuery() {
        return "noPushDown".equals(pushDownState);
    }

    /**
     * 检查是否为部分下推状态查询
     *
     * @return true-部分下推状态查询，false-其他状态
     */
    public boolean isPartPushDownQuery() {
        return "partPushDown".equals(pushDownState);
    }

    /**
     * 检查是否为已下推状态查询
     *
     * @return true-已下推状态查询，false-其他状态
     */
    public boolean isAllPushDownQuery() {
        return "allPushDown".equals(pushDownState);
    }
}
