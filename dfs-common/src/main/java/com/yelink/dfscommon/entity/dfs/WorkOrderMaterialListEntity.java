package com.yelink.dfscommon.entity.dfs;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatContainer;
import com.yelink.dfscommon.constant.ams.MaterialListStateEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownOrderStateEnum;
import com.yelink.dfscommon.dto.dfs.BomVO;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierInfoDTO;
import com.yelink.dfscommon.dto.pushdown.writeback.PushDownOrder;
import com.yelink.dfscommon.entity.wms.StockInventoryDetailEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-12-20 20:01
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_work_order_material_list")
@LogTag(bean = "workOrderMaterialListImpl", method = "getById", param = "id")
public class WorkOrderMaterialListEntity extends Model<WorkOrderMaterialListEntity> implements PushDownOrder {

    private static final long serialVersionUID = 1L;

    /**
     * 生产工单用料清单id
     */
    @TableId(value = "material_list_id", type = IdType.AUTO)
    private Integer materialListId;

    /**
     * 生产工单用料清单编号
     */
    @TableField(value = "material_list_code")
    private String materialListCode;

    /**
     * 生产工单用料清单状态（1-创建 2-生效 3-完成 4-关闭 5-取消）
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 状态名称
     */
    @TableField(exist = false)
    private String stateName;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    @TableField(value = "business_type")
    private String businessType;

    @ApiModelProperty("业务类型名称")
    @TableField(exist = false)
    @LogTag(name = "业务类型名称")
    private String businessTypeName;

    /**
     * 单据类型
     */
    @ApiModelProperty("单据类型")
    @TableField(value = "order_type")
    private String orderType;

    @ApiModelProperty("单据类型名称")
    @TableField(exist = false)
    @LogTag(name = "单据类型名称")
    private String orderTypeName;


    /**
     * 关联单据类型
     */
    @TableField(value = "relate_type")
    private String relateType;

    /**
     * 关联单据类型
     */
    @TableField(exist = false)
    private String relateTypeName;

    /**
     * 关联单据编号
     */
    @TableField(value = "relate_number")
    private String relateNumber;

    /**
     * 关联单据物料编码
     */
    @TableField(value = "relate_material_code")
    @UnitColumn
    private String relateMaterialCode;

    /**
     * 关联单据特征参数skuId
     */
    @TableField(value = "relate_sku_id")
    private Integer relateSkuId;

    /**
     * 关联的bomId
     */
    @TableField(value = "bom_id")
    private Integer bomId;

    /**
     * 关联的bom的即时修订次数
     */
    @TableField(value = "bom_version_revision")
    private Integer bomVersionRevision;

    /**
     * 关联的bom编码
     */
    @TableField(exist = false)
    private BomVO bomVO;

    /**
     * 关联单据数量
     */
    @TableField(value = "relate_quantity")
    @UnitFormatColumn
    private Double relateQuantity;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(value = "create_by")
    @LogTag(name = "创建人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String createBy;

    /**
     * 创建人名字
     */
    @ApiModelProperty("创建人名字")
    @TableField(exist = false)
    private String createName;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "update_by")
    @LogTag(name = "修改人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String updateBy;

    /**
     * 修改人名字
     */
    @ApiModelProperty("修改人名字")
    @TableField(exist = false)
    private String updateName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 实际审批人
     */
    @ApiModelProperty("实际审批人")
    @TableField(value = "approver")
    @LogTag(name = "批准人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String approver;

    /**
     * 批准人名字
     */
    @ApiModelProperty("批准人名字")
    @TableField(exist = false)
    private String approverName;

    /**
     * 实际审批人
     */
    @ApiModelProperty("实际审批人")
    @TableField(value = "actual_approver")
    @LogTag(name = "审核人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String actualApprover;

    /**
     * 实际审批人签名地址
     */
    @ApiModelProperty("实际审批人签名地址")
    @TableField(exist = false)
    private String actualApproverSignatureUrl;

    /**
     * 实际审批人名字
     */
    @ApiModelProperty("实际审批人名字")
    @TableField(exist = false)
    private String actualApproverName;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    @TableField(value = "approval_status")
    @LogTag(name = "审批状态")
    private Integer approvalStatus;

    /**
     * 审批状态名称
     */
    @ApiModelProperty("审批状态名称")
    @TableField(exist = false)
    private String approvalStatusName;

    /**
     * 审批建议
     */
    @ApiModelProperty("审批建议")
    @TableField(value = "approval_suggestion")
    @LogTag(name = "审批建议")
    private String approvalSuggestion;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    @TableField(value = "approval_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    /**
     * 关联的物料列表
     */
    @TableField(exist = false)
    @Valid
    @UnitFormatContainer
    private List<WorkOrderMaterialListMaterialEntity> relatedMaterialList;

    /**
     * 关联的物料
     */
    @TableField(exist = false)
    @Valid
    @UnitFormatContainer
    private WorkOrderMaterialListMaterialEntity relatedMaterial;

    /**
     * 物料字段
     */
    @TableField(exist = false)
    private MaterialEntity materialFields;

    /**
     * 物料关联仓库
     */
    @TableField(exist = false)
    private List<StockInventoryDetailEntity> stockInventoryDetailEntities;

    /**
     * 领料状态（已领料、部分领料、未领）
     */
    @ApiModelProperty("领料状态（已领料、部分领料、未领料）")
    @TableField(exist = false)
    private String takeOutState;

    /**
     * 工艺工序id
     */
    @TableField(exist = false)
    private String craftProcedureId;

    /**
     * 下推标识信息列表
     */
    @ApiModelProperty("下推标识信息列表")
    @TableField(exist = false)
    private List<PushDownIdentifierInfoDTO> pushDownIdentifierInfos;

    /**
     * 生产工单用料清单扩展字段1
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段1")
    @TableField(value = "material_list_extend_field_one")
    private String materialListExtendFieldOne;
    /**
     * 生产工单用料清单扩展字段2
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段2")
    @TableField(value = "material_list_extend_field_two")
    private String materialListExtendFieldTwo;
    /**
     * 生产工单用料清单扩展字段3
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段3")
    @TableField(value = "material_list_extend_field_three")
    private String materialListExtendFieldThree;
    /**
     * 生产工单用料清单扩展字段4
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段4")
    @TableField(value = "material_list_extend_field_four")
    private String materialListExtendFieldFour;
    /**
     * 生产工单用料清单扩展字段5
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段5")
    @TableField(value = "material_list_extend_field_five")
    private String materialListExtendFieldFive;

    /**
     * 生产工单用料清单扩展字段6
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段6")
    @TableField(value = "material_list_extend_field_six")
    private String materialListExtendFieldSix;
    /**
     * 生产工单用料清单扩展字段7
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段7")
    @TableField(value = "material_list_extend_field_seven")
    private String materialListExtendFieldSeven;
    /**
     * 生产工单用料清单扩展字段8
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段8")
    @TableField(value = "material_list_extend_field_eight")
    private String materialListExtendFieldEight;
    /**
     * 生产工单用料清单扩展字段9
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段9")
    @TableField(value = "material_list_extend_field_nine")
    private String materialListExtendFieldNine;
    /**
     * 生产工单用料清单扩展字段10
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段10")
    @TableField(value = "material_list_extend_field_ten")
    private String materialListExtendFieldTen;

    /**
     * 使用到的编码规则id
     *
     * @param
     * @return
     */
    @TableField(exist = false)
    @ExcelIgnore
    private Integer numberRuleId;

    /**
     * 扩展字段中文名
     */
    @TableField(exist = false)
    private String materialListExtendFieldOneName;
    @TableField(exist = false)
    private String materialListExtendFieldTwoName;
    @TableField(exist = false)
    private String materialListExtendFieldThreeName;
    @TableField(exist = false)
    private String materialListExtendFieldFourName;
    @TableField(exist = false)
    private String materialListExtendFieldFiveName;
    @TableField(exist = false)
    private String materialListExtendFieldSixName;
    @TableField(exist = false)
    private String materialListExtendFieldSevenName;
    @TableField(exist = false)
    private String materialListExtendFieldEightName;
    @TableField(exist = false)
    private String materialListExtendFieldNineName;
    @TableField(exist = false)
    private String materialListExtendFieldTenName;


    @Override
    public Serializable pkVal() {
        return this.materialListId;
    }

    @Override
    public PushDownOrderStateEnum calPushDownOrderState() {

        if(state == null) {
            return null;
        }
        MaterialListStateEnum stateEnum = MaterialListStateEnum.getByCode(state);
        switch (stateEnum) {
            case RELEASED:
                return PushDownOrderStateEnum.RELEASED;
            case FINISHED:
                return PushDownOrderStateEnum.FINISHED;
            case CLOSED:
                return PushDownOrderStateEnum.CLOSED;
            case CANCELED:
                return PushDownOrderStateEnum.CANCELED;
            default:
                return null;
        }
    }

}
