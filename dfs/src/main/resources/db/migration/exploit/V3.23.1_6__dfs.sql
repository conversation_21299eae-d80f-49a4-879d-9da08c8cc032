-- ================================================此脚本专为精度需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为精度需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为精度需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为精度需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为精度需求设定，如需写脚本，请移步到3.23.1.1=======================================================

-- =====工单=====
call proc_modify_column(
        'dfs_work_order',
        'plan_quantity',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `plan_quantity` double(22,10) NOT NULL DEFAULT ''0'' COMMENT ''计划数量''');

call proc_modify_column(
        'dfs_work_order',
        'finish_count',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `finish_count` double(22,10) DEFAULT ''0'' COMMENT ''已完成数量''');

call proc_modify_column(
        'dfs_work_order',
        'unqualified',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `unqualified` double(22,10) DEFAULT ''0'' COMMENT ''不合格数量''');

call proc_modify_column(
        'dfs_work_order',
        'input_total',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `input_total` double(22,10) DEFAULT ''0'' COMMENT ''投入数量''');

call proc_modify_column(
        'dfs_work_order',
        'product_count',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `product_count` double(22,10) DEFAULT NULL COMMENT ''排产数量''');

call proc_modify_column(
        'dfs_work_order',
        'pendent_quantity',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `pendent_quantity` double(22,10) DEFAULT NULL COMMENT ''待排数量''');

call proc_modify_column(
        'dfs_work_order',
        'in_stock_count',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `in_stock_count` double(22,10) DEFAULT NULL COMMENT ''流转数量''');

call proc_modify_column(
        'dfs_work_order',
        'picking_quantity',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `picking_quantity` double(22,10) DEFAULT ''0'' COMMENT ''已领料数''');

call proc_modify_column(
        'dfs_work_order',
        'inventory_quantity',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `inventory_quantity` double(22,10) DEFAULT ''0'' COMMENT ''已入库数''');


-- =====工单计划=====
call proc_modify_column(
        'dfs_work_order_plan',
        'plan_quantity',
        'ALTER TABLE `dfs_work_order_plan` MODIFY COLUMN `plan_quantity` double(22,10) NOT NULL DEFAULT ''0'' COMMENT ''计划数量''');


-- =====工单每日产量记录表=====
call proc_modify_column(
        'dfs_record_work_order_day_count',
        'count',
        'ALTER TABLE `dfs_record_work_order_day_count` MODIFY COLUMN `count` double(22,10) DEFAULT ''0'' COMMENT ''产出数''');
call proc_modify_column(
        'dfs_record_work_order_day_count',
        'unqualified',
        'ALTER TABLE `dfs_record_work_order_day_count` MODIFY COLUMN `unqualified` double(22,10) DEFAULT ''0'' COMMENT ''不合格数量''');
call proc_modify_column(
        'dfs_record_work_order_day_count',
        'repair_qualified_number',
        'ALTER TABLE `dfs_record_work_order_day_count` MODIFY COLUMN `repair_qualified_number` double(22,10) DEFAULT ''0'' COMMENT ''返修良品数''');
call proc_modify_column(
        'dfs_record_work_order_day_count',
        'input',
        'ALTER TABLE `dfs_record_work_order_day_count` MODIFY COLUMN `input` double(22,10) DEFAULT ''0'' COMMENT ''投入数''');


-- =====工单产线每日产量记录表=====
call proc_modify_column(
        'dfs_record_work_order_line_day_count',
        'count',
        'ALTER TABLE `dfs_record_work_order_line_day_count` MODIFY COLUMN `count` double(22,10) DEFAULT ''0'' COMMENT ''产出数''');
call proc_modify_column(
        'dfs_record_work_order_line_day_count',
        'unqualified',
        'ALTER TABLE `dfs_record_work_order_line_day_count` MODIFY COLUMN `unqualified` double(22,10) DEFAULT ''0'' COMMENT ''不合格数量''');
call proc_modify_column(
        'dfs_record_work_order_line_day_count',
        'repair_qualified_number',
        'ALTER TABLE `dfs_record_work_order_line_day_count` MODIFY COLUMN `repair_qualified_number` double(22,10) DEFAULT ''0'' COMMENT ''返修良品数''');
call proc_modify_column(
        'dfs_record_work_order_line_day_count',
        'input',
        'ALTER TABLE `dfs_record_work_order_line_day_count` MODIFY COLUMN `input` double(22,10) DEFAULT ''0'' COMMENT ''投入数''');


-- =====工单生产单元产量记录表=====
call proc_modify_column(
        'dfs_work_order_basic_unit_count',
        'count',
        'ALTER TABLE `dfs_work_order_basic_unit_count` MODIFY COLUMN `count` double(22,10) DEFAULT ''0'' COMMENT ''产出数''');
call proc_modify_column(
        'dfs_work_order_basic_unit_count',
        'unqualified',
        'ALTER TABLE `dfs_work_order_basic_unit_count` MODIFY COLUMN `unqualified` double(22,10) DEFAULT ''0'' COMMENT ''不合格数量''');
call proc_modify_column(
        'dfs_work_order_basic_unit_count',
        'repair_qualified_number',
        'ALTER TABLE `dfs_work_order_basic_unit_count` MODIFY COLUMN `repair_qualified_number` double(22,10) DEFAULT ''0'' COMMENT ''返修良品数''');
call proc_modify_column(
        'dfs_work_order_basic_unit_count',
        'input',
        'ALTER TABLE `dfs_work_order_basic_unit_count` MODIFY COLUMN `input` double(22,10) DEFAULT ''0'' COMMENT ''投入数''');


-- =====工单生产单元产量每日记录表=====
call proc_modify_column(
        'dfs_work_order_basic_unit_day_count',
        'count',
        'ALTER TABLE `dfs_work_order_basic_unit_day_count` MODIFY COLUMN `count` double(22,10) DEFAULT ''0'' COMMENT ''产出数''');
call proc_modify_column(
        'dfs_work_order_basic_unit_day_count',
        'unqualified',
        'ALTER TABLE `dfs_work_order_basic_unit_day_count` MODIFY COLUMN `unqualified` double(22,10) DEFAULT ''0'' COMMENT ''不合格数量''');
call proc_modify_column(
        'dfs_work_order_basic_unit_day_count',
        'repair_qualified_number',
        'ALTER TABLE `dfs_work_order_basic_unit_day_count` MODIFY COLUMN `repair_qualified_number` double(22,10) DEFAULT ''0'' COMMENT ''返修良品数''');
call proc_modify_column(
        'dfs_work_order_basic_unit_day_count',
        'input',
        'ALTER TABLE `dfs_work_order_basic_unit_day_count` MODIFY COLUMN `input` double(22,10) DEFAULT ''0'' COMMENT ''投入数''');


-- =====工单投入数记录表=====
call proc_modify_column(
        'dfs_work_order_input',
        'input',
        'ALTER TABLE `dfs_work_order_input` MODIFY COLUMN `input` double(22,10) DEFAULT ''0'' COMMENT ''投入数''');


-- =====批次=====
call proc_modify_column(
        'dfs_bar_code',
        'count',
        'ALTER TABLE `dfs_bar_code` MODIFY COLUMN `count` double(22,10) DEFAULT ''0'' COMMENT ''批次默认数''');
call proc_modify_column(
        'dfs_bar_code',
        'finish_count',
        'ALTER TABLE `dfs_bar_code` MODIFY COLUMN `finish_count` double(22,10) DEFAULT ''0'' COMMENT ''生产数量''');
call proc_modify_column(
        'dfs_bar_code',
        'unqualified',
        'ALTER TABLE `dfs_bar_code` MODIFY COLUMN `unqualified` double(22,10) DEFAULT ''0'' COMMENT ''不良数量''');
call proc_modify_column(
        'dfs_bar_code',
        'current_inventory',
        'ALTER TABLE `dfs_bar_code` MODIFY COLUMN `current_inventory` double(22,10) DEFAULT NULL COMMENT ''当前库存''');
call proc_modify_column(
        'dfs_bar_code',
        'outbound_quantity',
        'ALTER TABLE `dfs_bar_code` MODIFY COLUMN `outbound_quantity` double(22,10) DEFAULT NULL COMMENT ''出库数量''');
call proc_modify_column(
        'dfs_bar_code',
        'receipt_quantity',
        'ALTER TABLE `dfs_bar_code` MODIFY COLUMN `receipt_quantity` double(22,10) DEFAULT NULL COMMENT ''入库数量''');
call proc_modify_column(
        'dfs_bar_code',
        'inventory_quantity',
        'ALTER TABLE `dfs_bar_code` MODIFY COLUMN `inventory_quantity` double(22,10) DEFAULT NULL COMMENT ''入库数量''');


-- =====工单报工=====
call proc_modify_column(
        'dfs_report_line',
        'finish_count',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `finish_count` double(22,10) DEFAULT ''0'' COMMENT ''完成数量''');
call proc_modify_column(
        'dfs_report_line',
        'unqualified',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `unqualified` double(22,10) DEFAULT ''0'' COMMENT ''不合格数量''');


-- =====工单工序报工=====
call proc_modify_column(
        'dfs_report_line_procedure',
        'finish_count',
        'ALTER TABLE `dfs_report_line_procedure` MODIFY COLUMN `finish_count` double(22,10) DEFAULT ''0'' COMMENT ''完成数量''');
call proc_modify_column(
        'dfs_report_line_procedure',
        'unqualified',
        'ALTER TABLE `dfs_report_line_procedure` MODIFY COLUMN `unqualified` double(22,10) DEFAULT ''0'' COMMENT ''不合格数量''');