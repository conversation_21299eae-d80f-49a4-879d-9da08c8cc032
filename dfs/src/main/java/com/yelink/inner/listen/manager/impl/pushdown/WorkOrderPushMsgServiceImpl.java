package com.yelink.inner.listen.manager.impl.pushdown;


import com.yelink.dfs.constant.ValueChainKafkaMessageConstants;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierFullPathEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierStateEnum;
import com.yelink.dfscommon.entity.dfs.OrderPushDownIdentifierEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.dfscommon.utils.WrapperUtil;
import com.yelink.inner.listen.manager.OrderPushDownMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(ValueChainKafkaMessageConstants.WORK_ORDER_PUSH_MSG_SERVICE)
public class WorkOrderPushMsgServiceImpl implements OrderPushDownMessageService {

    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OrderPushDownIdentifierService pushDownIdentifierService;
    @Resource
    private OrderPushDownRecordService orderPushDownRecordService;

    @Async
    @Override
    public void deal(List<OrderPushDownRecordEntity> recordEntities) {
        if (CollectionUtils.isEmpty(recordEntities)) {
            return;
        }

        try {
            // 1. 按工单ID（物料行ID）和目标单据类型分组传入的下推记录
            Map<Integer, Map<String, List<OrderPushDownRecordEntity>>> recordsByWorkOrderIdAndTargetType = recordEntities.stream()
                    .filter(record -> Objects.nonNull(record.getSourceOrderMaterialId()) && Objects.nonNull(record.getTargetOrderType()))
                    .collect(Collectors.groupingBy(
                            OrderPushDownRecordEntity::getSourceOrderMaterialId,
                            Collectors.groupingBy(OrderPushDownRecordEntity::getTargetOrderType)
                    ));

            if (recordsByWorkOrderIdAndTargetType.isEmpty()) {
                log.warn("未找到有效的工单ID和目标单据类型，跳过处理");
                return;
            }

            // 2. 查询涉及的工单信息
            List<Integer> workOrderIds = new ArrayList<>(recordsByWorkOrderIdAndTargetType.keySet());
            List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                    .in(WorkOrderEntity::getWorkOrderId, workOrderIds).list();
            if (CollectionUtils.isEmpty(workOrderEntities)) {
                log.warn("未找到对应的工单信息，工单ID: {}", workOrderIds);
                return;
            }

            // 3. 为每个工单的每种目标单据类型计算下推状态并更新标识
            List<OrderPushDownIdentifierEntity> identifierEntities = new ArrayList<>();
            for (WorkOrderEntity workOrder : workOrderEntities) {
                Map<String, List<OrderPushDownRecordEntity>> targetTypeRecords = recordsByWorkOrderIdAndTargetType.get(workOrder.getWorkOrderId());
                if (targetTypeRecords == null) {
                    continue;
                }

                // 为每种目标单据类型分别处理
                for (Map.Entry<String, List<OrderPushDownRecordEntity>> entry : targetTypeRecords.entrySet()) {
                    String targetOrderType = entry.getKey();

                    // 查询该工单对该目标单据类型的所有下推记录
                    List<OrderPushDownRecordEntity> allTypeRecords = orderPushDownRecordService.lambdaQuery()
                            .eq(OrderPushDownRecordEntity::getSourceOrderMaterialId, workOrder.getWorkOrderId().toString())
                            .eq(OrderPushDownRecordEntity::getSourceOrderType, OrderNumTypeEnum.WORK_ORDER.getTypeCode())
                            .eq(OrderPushDownRecordEntity::getTargetOrderType, targetOrderType)
                            .list();

                    // 计算该目标单据类型的下推状态
                    String pushDownState = calculatePushDownState(workOrder, allTypeRecords);

                    // 创建或更新下推标识
                    OrderPushDownIdentifierEntity identifierEntity = OrderPushDownIdentifierEntity.builder()
                            .orderType(OrderNumTypeEnum.WORK_ORDER.getTypeCode())
                            .orderMaterialId(workOrder.getWorkOrderId().toString())
                            .targetOrderType(targetOrderType)
                            .state(pushDownState)
                            .build();

                    identifierEntities.add(identifierEntity);
                }
            }

            // 4. 批量保存或更新下推标识
            if (CollectionUtils.isNotEmpty(identifierEntities)) {
                updatePushDownIdentifiers(identifierEntities);
            }

        } catch (Exception e) {
            log.error("处理工单下推标识时发生异常", e);
        }
    }

    /**
     * 计算工单的下推状态
     *
     * @param workOrder 工单信息
     * @param pushDownRecords 下推记录列表
     * @return 下推状态
     */
    private String calculatePushDownState(WorkOrderEntity workOrder, List<OrderPushDownRecordEntity> pushDownRecords) {
        if (CollectionUtils.isEmpty(pushDownRecords)) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        }

        // 过滤异常记录，计算有效下推数量
        List<OrderPushDownRecordEntity> validRecords = pushDownRecords.stream()
                .filter(record -> !Boolean.TRUE.equals(record.getIsAbnormal()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validRecords)) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        }

        // 去重并计算下推数量总和
        List<OrderPushDownRecordEntity> distinctRecords = validRecords.stream()
                .filter(WrapperUtil.distinctByKey(OrderPushDownRecordEntity::getPushDownCode))
                .collect(Collectors.toList());

        double totalPushDownQuantity = distinctRecords.stream()
                .mapToDouble(OrderPushDownRecordEntity::getPushDownQuantity)
                .sum();

        double planQuantity = workOrder.getPlanQuantity() != null ? workOrder.getPlanQuantity() : 0.0;

        // 判断下推状态
        if (totalPushDownQuantity == 0) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        } else if (totalPushDownQuantity < planQuantity) {
            return PushDownIdentifierStateEnum.PART_PUSH_DOWN.getCode();
        } else {
            return PushDownIdentifierStateEnum.ALL_PUSH_DOWN.getCode();
        }
    }

    /**
     * 更新下推标识
     *
     * @param identifierEntities 下推标识实体列表
     */
    private void updatePushDownIdentifiers(List<OrderPushDownIdentifierEntity> identifierEntities) {
        if (CollectionUtils.isEmpty(identifierEntities)) {
            return;
        }

        // 过滤掉未下推状态的数据，未下推状态不需要保存到数据库
        List<OrderPushDownIdentifierEntity> validEntities = identifierEntities.stream()
                .filter(entity -> !PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode().equals(entity.getState()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validEntities)) {
            log.debug("所有下推标识都是未下推状态，无需保存到数据库");
            return;
        }

        // 批量查询现有记录
        List<OrderPushDownIdentifierEntity> existingEntities = pushDownIdentifierService.lambdaQuery()
                .eq(OrderPushDownIdentifierEntity::getOrderType, validEntities.get(0).getOrderType())
                .in(OrderPushDownIdentifierEntity::getOrderMaterialId,
                    validEntities.stream().map(OrderPushDownIdentifierEntity::getOrderMaterialId).distinct().collect(Collectors.toList()))
                .list();
        // 构建现有记录的映射
        Map<String, OrderPushDownIdentifierEntity> existingMap = existingEntities.stream()
                .collect(Collectors.toMap(
                        entity -> entity.getOrderType() + "_" + entity.getOrderMaterialId() + "_" + entity.getTargetOrderType(),
                        entity -> entity
                ));
        // 分离需要新增和更新的记录
        List<OrderPushDownIdentifierEntity> toInsert = new ArrayList<>();
        List<OrderPushDownIdentifierEntity> toUpdate = new ArrayList<>();

        for (OrderPushDownIdentifierEntity entity : validEntities) {
            String key = entity.getOrderType() + "_" + entity.getOrderMaterialId() + "_" + entity.getTargetOrderType();
            OrderPushDownIdentifierEntity existingEntity = existingMap.get(key);

            if (existingEntity != null) {
                // 只有状态发生变化时才更新
                if (!entity.getState().equals(existingEntity.getState())) {
                    existingEntity.setState(entity.getState());
                    toUpdate.add(existingEntity);
                }
            } else {
                toInsert.add(entity);
            }
        }

        // 批量新增
        if (!CollectionUtils.isEmpty(toInsert)) {
            pushDownIdentifierService.saveBatch(toInsert);
            log.debug("批量新增下推标识: {} 条记录", toInsert.size());
        }
        // 批量更新
        if (!CollectionUtils.isEmpty(toUpdate)) {
            pushDownIdentifierService.updateBatchById(toUpdate);
            log.debug("批量更新下推标识: {} 条记录", toUpdate.size());
        }
        // 处理需要删除的未下推记录
        handleNoPushDownRecords(identifierEntities, existingMap);
    }

    /**
     * 处理未下推记录的删除逻辑
     * 当状态变为未下推时，需要删除数据库中对应的记录
     *
     * @param allEntities 所有下推标识实体（包括未下推状态）
     * @param existingMap 现有记录映射
     */
    private void handleNoPushDownRecords(List<OrderPushDownIdentifierEntity> allEntities,
                                       Map<String, OrderPushDownIdentifierEntity> existingMap) {
        // 找出状态为未下推且数据库中存在记录的实体
        List<Integer> toDeleteIds = new ArrayList<>();
        for (OrderPushDownIdentifierEntity entity : allEntities) {
            if (PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode().equals(entity.getState())) {
                String key = entity.getOrderType() + "_" + entity.getOrderMaterialId() + "_" + entity.getTargetOrderType();
                OrderPushDownIdentifierEntity existingEntity = existingMap.get(key);
                if (existingEntity != null) {
                    toDeleteIds.add(existingEntity.getId());
                }
            }
        }
        // 批量删除未下推状态的记录
        if (!CollectionUtils.isEmpty(toDeleteIds)) {
            pushDownIdentifierService.removeByIds(toDeleteIds);
            log.debug("批量删除未下推状态记录: {} 条", toDeleteIds.size());
        }
    }
}
