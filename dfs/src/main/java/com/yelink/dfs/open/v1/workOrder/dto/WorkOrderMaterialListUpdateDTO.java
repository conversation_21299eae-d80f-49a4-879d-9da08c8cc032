package com.yelink.dfs.open.v1.workOrder.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatContainer;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListMaterialEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-12-20 20:01
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderMaterialListUpdateDTO {

    /**
     * 生产订单用料清单id
     */
    @ApiModelProperty(value = "生产订单用料清单id", required = true)
    @NotBlank(message = "id不能为空")
    @TableId(value = "material_list_id", type = IdType.AUTO)
    private Integer materialListId;

    /**
     * 生产订单用料清单编号
     */
    @ApiModelProperty(value = "生产订单用料清单编号", required = true)
    @NotBlank(message = "编号不能为空")
    @TableField(value = "material_list_code")
    private String materialListCode;

    /**
     * 生产订单用料清单状态（1-创建 2-生效 3-完成 4-关闭 5-取消）
     */
    @ApiModelProperty(value = "生产订单用料清单状态（1-创建 2-生效 3-完成 4-关闭 5-取消）", required = true)
    @NotBlank(message = "状态不能为空")
    @TableField(value = "state")
    private Integer state;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String orderType;

    /**
     * 关联单据类型
     */
    @ApiModelProperty(value = "关联单据类型", required = true)
    @NotBlank(message = "关联单据类型不能为空")
    @TableField(value = "relate_type")
    private String relateType;

    /**
     * 关联单据编号
     */
    @ApiModelProperty(value = "关联单据编号", required = true)
    @NotBlank(message = "关联单据编号不能为空")
    @TableField(value = "relate_number")
    private String relateNumber;

    /**
     * 关联单据物料编码
     */
    @ApiModelProperty(value = "关联单据物料编码", required = true)
    @NotBlank(message = "关联单据物料编码不能为空")
    @TableField(value = "relate_material_code")
    @UnitColumn
    private String relateMaterialCode;

    /**
     * 关联单据数量
     */
    @ApiModelProperty(value = "关联单据数量", required = true)
    @NotBlank(message = "关联单据数量不能为空")
    @TableField(value = "relate_quantity")
    @UnitFormatColumn
    private Double relateQuantity;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(value = "create_by")
    @LogTag(name = "创建人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "update_by")
    @LogTag(name = "修改人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 生产工单用料清单扩展字段1
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段1")
    private String materialListExtendFieldOne;
    /**
     * 生产工单用料清单扩展字段2
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段2")
    private String materialListExtendFieldTwo;
    /**
     * 生产工单用料清单扩展字段3
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段3")
    private String materialListExtendFieldThree;
    /**
     * 生产工单用料清单扩展字段4
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段4")
    private String materialListExtendFieldFour;
    /**
     * 生产工单用料清单扩展字段5
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段5")
    private String materialListExtendFieldFive;

    /**
     * 生产工单用料清单扩展字段6
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段6")
    private String materialListExtendFieldSix;
    /**
     * 生产工单用料清单扩展字段7
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段7")
    private String materialListExtendFieldSeven;
    /**
     * 生产工单用料清单扩展字段8
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段8")
    private String materialListExtendFieldEight;
    /**
     * 生产工单用料清单扩展字段9
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段9")
    private String materialListExtendFieldNine;
    /**
     * 生产工单用料清单扩展字段10
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段10")
    private String materialListExtendFieldTen;

    /**
     * 关联的物料列表
     */
    @ApiModelProperty(value = "关联的物料列表", required = true)
    @NotBlank(message = "关联的物料列表不能为空")
    @TableField(exist = false)
    @Valid
    @UnitFormatContainer
    private List<WorkOrderMaterialListMaterialEntity> relatedMaterialList;

    /**
     * 关联单据特征参数skuId
     */
    @TableField(value = "relate_sku_id")
    private Integer relateSkuId = 0;

}
