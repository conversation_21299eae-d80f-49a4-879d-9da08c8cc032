package com.yelink.dfs.open.v1.workOrder.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatContainer;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListMaterialEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-12-20 20:01
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderMaterialListInsertDTO {

    /**
     * 生产工单用料清单编号
     */
    @ApiModelProperty(value = "生产工单用料清单编号", required = true)
    @NotBlank(message = "编号不能为空")
    private String materialListCode;

    /**
     * 关联单据类型
     */
    @ApiModelProperty(value = "关联单据类型", required = true)
    @NotBlank(message = "关联单据类型不能为空")
    private String relateType;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String orderType;

    /**
     * 关联单据编号
     */
    @ApiModelProperty(value = "关联单据编号", required = true)
    @NotBlank(message = "关联单据编号不能为空")
    private String relateNumber;

    /**
     * 关联单据物料编码
     */
    @ApiModelProperty(value = "关联单据物料编码", required = true)
    @NotBlank(message = "关联单据物料编码不能为空")
    @UnitColumn
    private String relateMaterialCode;

    /**
     * 关联单据数量
     */
    @ApiModelProperty(value = "关联单据数量", required = true)
    @NotBlank(message = "关联单据数量不能为空")
    @UnitFormatColumn
    private Double relateQuantity;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @LogTag(name = "创建人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @LogTag(name = "修改人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 生产工单用料清单扩展字段1
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段1")
    private String materialListExtendFieldOne;
    /**
     * 生产工单用料清单扩展字段2
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段2")
    private String materialListExtendFieldTwo;
    /**
     * 生产工单用料清单扩展字段3
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段3")
    private String materialListExtendFieldThree;
    /**
     * 生产工单用料清单扩展字段4
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段4")
    private String materialListExtendFieldFour;
    /**
     * 生产工单用料清单扩展字段5
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段5")
    private String materialListExtendFieldFive;

    /**
     * 生产工单用料清单扩展字段6
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段6")
    private String materialListExtendFieldSix;
    /**
     * 生产工单用料清单扩展字段7
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段7")
    private String materialListExtendFieldSeven;
    /**
     * 生产工单用料清单扩展字段8
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段8")
    private String materialListExtendFieldEight;
    /**
     * 生产工单用料清单扩展字段9
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段9")
    private String materialListExtendFieldNine;
    /**
     * 生产工单用料清单扩展字段10
     */
    @ApiModelProperty(value = "生产工单用料清单扩展字段10")
    private String materialListExtendFieldTen;

    /**
     * 关联的物料列表
     */
    @ApiModelProperty(value = "关联的物料列表", required = true)
    @NotBlank(message = "关联的物料列表不能为空")
    @Valid
    @UnitFormatContainer
    private List<WorkOrderMaterialListMaterialEntity> relatedMaterialList;

    /**
     * 关联单据特征参数skuId
     */
    private Integer relateSkuId = 0;

}
