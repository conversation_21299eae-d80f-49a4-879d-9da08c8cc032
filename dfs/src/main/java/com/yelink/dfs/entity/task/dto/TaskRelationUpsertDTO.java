package com.yelink.dfs.entity.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;


/**
 * 任务关联项 新增/更新
 * <AUTHOR>
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TaskRelationUpsertDTO {

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "主单据类型", required = true)
    @NotBlank(message = "主单据类型不能为空")
    private String orderCategory;

    /**
     * 单据编码
     */
    @ApiModelProperty(value = "主单据编码", required = true)
    @NotBlank(message = "主单据编码不能为空")
    private String orderNumber;

    @ApiModelProperty("物料行Id")
    private Long materialLineId;
    /**
     * 关联类型
     */
    @ApiModelProperty(value = "关联类型", notes = "当applicationId为空时，该项必填")
    private String relateType;

    @ApiModelProperty(value = "applicationId")
    private String applicationId;


    /**
     * 关联id
     */
    @ApiModelProperty(value = "关联id", required = true)
    @NotBlank(message = "关联id不能为空")
    private String relateId;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String username;







}
