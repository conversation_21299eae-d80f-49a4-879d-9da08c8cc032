package com.yelink.dfs.entity.order;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfs.entity.order.dto.OperationOrderDTO;
import com.yelink.dfs.entity.pack.PackageSchemeEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.open.v2.order.vo.WorkOrderRelevanceVO;
import com.yelink.dfs.service.common.OrderChangeLog;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatContainer;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownOrderStateEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierInfoDTO;
import com.yelink.dfscommon.dto.pushdown.writeback.PushDownOrder;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.wms.StockInAndOutEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021-03-17 11:59
 */

@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_work_order")
@EqualsAndHashCode(callSuper = false)
@LogTag(interfaceClass = WorkOrderService.class, method = "getSimpleWorkOrderByNumber", param = "workOrderNumber")
public class WorkOrderEntity extends Model<WorkOrderEntity> implements Serializable, PushDownOrder {

    private static final long serialVersionUID = 1L;

    /**
     * 工单id
     */
    @ApiModelProperty("工单id,工单物料行id")
    @TableId(value = "work_order_id", type = IdType.AUTO)
    @NotNull(groups = {Update.class}, message = "工单id不能为空")
    private Integer workOrderId;

    /**
     * 工单号
     */
    @ApiModelProperty("工单号")
    @TableField(value = "work_order_number")
    private String workOrderNumber;

    /**
     * 工单名称
     */
    @ApiModelProperty("工单名称")
    @TableField(value = "work_order_name")
    @LogTag(name = "工单名称")
    private String workOrderName;

    /**
     * 开始时间
     */
    @ApiModelProperty("计划开始时间")
    @LogTag(name = "计划开始时间")
    @TableField(value = "start_date")
    @OrderChangeLog(title = "计划开始时间调整")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(groups = {Insert.class, Update.class}, message = "开始时间不能为空")
    private Date startDate;

    /**
     * 截止时间
     */
    @ApiModelProperty("计划结束时间")
    @TableField(value = "end_date")
    @LogTag(name = "计划结束时间")
    @OrderChangeLog(title = "计划结束时间调整")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(groups = {Insert.class, Update.class}, message = "截止时间不能为空")
    private Date endDate;

    /**
     * 实际开始时间
     */
    @ApiModelProperty("实际开始时间")
    @TableField(value = "actual_start_date")
    @LogTag(name = "实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualStartDate;

    /**
     * 实际结束时间
     */
    @ApiModelProperty("实际结束时间")
    @TableField(value = "actual_end_date")
    @LogTag(name = "实际结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualEndDate;

    /**
     * 状态
     * 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消
     */
    @ApiModelProperty("状态 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消")
    @TableField(value = "state")
    @NotNull(groups = {Update.class}, message = "状态不能为空")
    private Integer state;

    /**
     * 派工状态
     */
    @ApiModelProperty("派工状态 toBeAssigned-待派工、assigned-已派工")
    @TableField(value = "assignment_state")
    private String assignmentState;

    /**
     * 存在项目合同 0 不存在 1 存在
     */
    @ApiModelProperty("存在项目合同")
    @TableField(value = "project_contract")
    private Boolean projectContract;

    /**
     * 项目定义ID
     */
    @ApiModelProperty("项目定义ID")
    @TableField(exist = false)
    private String projectDefineId;

    /**
     * 合同ID
     */
    @ApiModelProperty("合同ID")
    @TableField(exist = false)
    private String contractId;

    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String projectDefineName;

    /**
     * 合同名称
     */
    @TableField(exist = false)
    private String contractName;

    /**
     * 项目任务编码
     */
    @ApiModelProperty("项目任务编码")
    @TableField(exist = false)
    private String projectNodeIds;

    /**
     * 项目任务名称
     */
    @ApiModelProperty("项目任务名称")
    @TableField(exist = false)
    private String projectNodeName;

    /**
     * 派工状态名称
     */
    @ApiModelProperty("派工状态名称")
    @TableField(exist = false)
    private String assignmentStateName;

    /**
     * 产线模型编号
     */
    @ApiModelProperty("产线编号")
    @TableField(exist = false)
    private String lineModelCode;
    /**
     * 产线模型名称
     */
    @ApiModelProperty("产线模型名称")
    @TableField(exist = false)
    private String lineModelName;
    /**
     * 产线编号
     */
    @ApiModelProperty("产线编号")
    @TableField(value = "line_code")
    private String lineCode;

    /**
     * 产线名称
     */
    @ApiModelProperty("产线名称")
    @TableField(value = "line_name")
    @LogTag(name = "产线名称")
    @OrderChangeLog(title = "产线调整")
    private String lineName;

    /**
     * 产线id
     */
    @ApiModelProperty("产线id")
    @TableField(value = "line_id")
    private Integer lineId;
    /**
     * 产线id
     */
//    @TableField(exist = false)
//    private String lineIds;

    /**
     * 计划数量
     */
    @ApiModelProperty("计划数量")
    @TableField(value = "plan_quantity")
    @LogTag(name = "计划数量")
    @NotNull(groups = {Insert.class, Update.class}, message = "计划数量不能为空")
    @OrderChangeLog(title = "计划数量调整")
    @UnitFormatColumn
    private Double planQuantity;

    /**
     * 计划批数
     */
    @ApiModelProperty("计划批数")
    @TableField(value = "planned_batches")
    private Double plannedBatches;

    /**
     * 每批计划数
     */
    @ApiModelProperty("每批计划数")
    @TableField(value = "plans_per_batch")
    private Double plansPerBatch;

    /**
     * 实际批数
     */
    @ApiModelProperty("实际批数")
    @TableField(value = "actual_batches")
    private Double actualBatches;

    /**
     * 关联销售订单的物料行ID
     */
    @ApiModelProperty("关联销售订单的物料行ID")
    @TableField(value = "relate_order_material_id")
    private Integer relateOrderMaterialId;

    /**
     * 物料编号
     */
    @ApiModelProperty("物料编号")
    @TableField(value = "material_code")
    @LogTag(name = "物料编号")
    @NotNull(groups = {Insert.class, Update.class}, message = "物料编号不能为空")
    @UnitColumn
    private String materialCode;

    @TableField(exist = false)
    private String materialName;

    /**
     * 不合格量
     */
    @ApiModelProperty("不合格量")
    @TableField(value = "unqualified")
    @LogTag(name = "不合格数量")
    @UnitFormatColumn
    private Double unqualified;

    /**
     * 关联的父工单
     */
    @ApiModelProperty("关联的父工单")
    @TableField(value = "pnumber")
//    @LogTag(name = "关联的父工单号")
    private String pnumber;

    /**
     * 关联的子工单
     */
//    @TableField(exist = false)
//    private List<WorkOrderEntity> subWorkOrder;

    /**
     * 是否有父工单 1是 0否
     */
    @ApiModelProperty("是否有父工单 1是 0否")
    @TableField(value = "is_pid")
    private Boolean isPid;

    /**
     * 子工单顺序
     */
    @ApiModelProperty("子工单顺序")
    @TableField(value = "id_sequence")
    @LogTag(name = "子工单顺序")
    private String idSequence;

    /**
     * 进度（完成率）
     */
    @ApiModelProperty("进度（完成率）")
    @TableField(value = "progress")
    private Double progress;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value = "remark")
    @LogTag(name = "备注")
    private String remark;

    /**
     * 物料当前库存
     */
    @ApiModelProperty("物料当前库存")
    @TableField(value = "current_inventory")
    private Double currentInventory;
    /**
     * 物料是否齐套
     */
    @ApiModelProperty("物料是否齐套")
    @TableField(value = "material_is_complete")
    private Boolean materialIsComplete;
    /**
     * 物料齐套数量
     */
    @ApiModelProperty("物料齐套数量")
    @TableField(value = "material_complete_num")
    private Double materialCompleteNum;
    /**
     * 物料欠套数量
     */
    @ApiModelProperty("物料欠套数量")
    @TableField(value = "material_owe_num")
    private Double materialOweNum;
    /**
     * 理论工作时长
     */
    @ApiModelProperty("理论工作时长")
    @TableField(value = "theoretical_working_hours")
    private Double theoreticalWorkingHours;

    /**
     * 类型（该类型用于指标）
     */
    @ApiModelProperty("类型（该类型用于指标）")
    @TableField(value = "type")
    private String type;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    @TableField(value = "business_type")
    private String businessType;

    @ApiModelProperty("业务类型名称")
    @TableField(exist = false)
    @LogTag(name = "业务类型名称")
    private String businessTypeName;

    /**
     * 单据类型
     */
    @ApiModelProperty("单据类型")
    @TableField(value = "order_type")
    private String orderType;

    @ApiModelProperty("单据类型名称")
    @TableField(exist = false)
    @LogTag(name = "单据类型名称")
    private String orderTypeName;

    /**
     * CreateBy
     */
    @ApiModelProperty("CreateBy")
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    @TableField(exist = false)
    private String createName;

    /**
     * CreateDate
     */
    @ApiModelProperty("CreateDate")
    @TableField(value = "create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 更新人
     */
    @ApiModelProperty("UpdateBy")
    @TableField(value = "update_by")
    private String updateBy;

    @TableField(exist = false)
    private String updateByName;

    /**
     * 更新时间
     */
    @ApiModelProperty("UpdateDate")
    @TableField(value = "update_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 删除人
     */
    @TableField(exist = false)
    private String deleteName;

    /**
     * 删除时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deleteTime;

    /**
     * 有效工时
     */
    @ApiModelProperty("有效工时")
    @TableField(value = "effective_hours")
    private Double effectiveHours;

    /**
     * 计划员真实姓名
     */
    @ApiModelProperty("计划员真实姓名")
    @TableField(value = "mag_nickname")
    private String magNickname;

    /**
     * 计划员手机号
     */
    @ApiModelProperty("计划员手机号")
    @TableField(value = "mag_phone")
    private String magPhone;

    /**
     * 计划员账号
     */
    @ApiModelProperty("计划员账号")
    @TableField(value = "mag_name")
    @LogTag(name = "计划员", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String magName;

    /**
     * 已完成数量
     */
    @ApiModelProperty("已完成数量")
    @TableField(value = "finish_count")
    @UnitFormatColumn
    private Double finishCount;

    /**
     * 投入数量
     */
    @ApiModelProperty("投入数量")
    @TableField(value = "input_total")
    @UnitFormatColumn
    private Double inputTotal;

    /**
     * 工单接收人（工单完成时通知人员账号，逗号隔开）
     */
    @ApiModelProperty("工单接收人（工单完成时通知人员账号，逗号隔开）")
    @TableField(value = "notice_username")
    private String noticeUsername;

    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    @TableField(value = "priority")
    @LogTag(name = "优先级")
    private String priority;

    /**
     * 工单状态名称 0-创建 1-发放 2-完成 3-关闭 4-取消
     */
    @ApiModelProperty("工单状态名称 0-创建 1-发放 2-完成 3-关闭 4-取消")
    @TableField(exist = false)
    @LogTag(name = "状态")
    @OrderChangeLog(title = "状态变更")
    private String stateName;

    public String getStateName() {
        if (this.state != null) {
            return WorkOrderStateEnum.getNameByCode(this.getState());
        }
        return null;
    }

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 客户编号
     */
    @ApiModelProperty("客户编号")
    @TableField(value = "customer_code")
    private String customerCode;

    /**
     * 计划交付时间/计划开始生产时间
     */
    @ApiModelProperty("计划交付时间/计划开始生产时间")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handOverDate;

    /**
     * 订单号，用逗号隔开
     */
    @ApiModelProperty("订单号，用逗号隔开")
    @TableField(value = "product_order_number")
    private String productOrderNumber;

    @TableField(exist = false)
    private Integer productOrderId;

    /**
     * 销售订单号，用逗号隔开
     */
    @ApiModelProperty("销售订单号，用逗号隔开")
    @TableField(value = "sale_order_number")
    private String saleOrderNumber;


    @TableField(exist = false)
    private Integer saleOrderId;

    /**
     * 包装数
     */
    @ApiModelProperty("包装数")
    @TableField(value = "package_quantity")
    private Double packageQuantity;

    /**
     * 报工系数
     */
    @ApiModelProperty("报工系数")
    @TableField(value = "coefficient")
    @OrderChangeLog(title = "系数调整")
    private Double coefficient;

    /**
     * 是否备料
     */
    @ApiModelProperty("是否备料")
    @LogTag(name = "是否备料")
    @TableField(value = "prepared")
    private Boolean prepared;

    /**
     * 是否发料
     */
    @ApiModelProperty("是否发料")
    @LogTag(name = "是否发料")
    @TableField(value = "assigned")
    private Boolean assigned;

    /**
     * erp关联单据编号
     */
    @ApiModelProperty("erp关联单据编号")
    @TableField(value = "erp_document_code")
    @LogTag(name = "erp关联单据编号")
    private String erpDocumentCode;

    /**
     * 是否下发
     */
    @ApiModelProperty("是否下发")
    @LogTag(name = "是否下发")
    @TableField(value = "issue")
    private Boolean issue;

    /**
     * 生产订单列表
     */
    @ApiModelProperty("生产订单列表")
    @TableField(exist = false)
    private List<ProductOrderEntity> productOrderList;
    /**
     * 销售订单列表
     */
    @ApiModelProperty("销售订单列表")
    @TableField(exist = false)
    private List<SaleOrderEntity> saleOrderList;

    @ApiModelProperty("物料列表")
    @TableField(exist = false)
    private List<MaterialEntity> materialEntityList;

    /**
     * 领料出库单列表
     */
    @ApiModelProperty("领料出库单列表")
    @TableField(exist = false)
    private List<StockInAndOutEntity> takeOutList;

    /**
     * 入库单列表
     */
    @ApiModelProperty("入库单列表")
    @TableField(exist = false)
    private List<StockInAndOutEntity> inputList;

    /**
     * 退库单列表
     */
    @ApiModelProperty("退库单列表")
    @TableField(exist = false)
    private List<StockInAndOutEntity> returnList;

    /**
     * 计量重量(工单物料计划数量记录计算用)
     */
    @ApiModelProperty("计量重量(工单物料计划数量记录计算用)")
    @TableField(exist = false)
    private Double measurementQuantity;

    /**
     * 计量单位(工单物料计划数量记录计算用)
     */
    @ApiModelProperty("计量单位(工单物料计划数量记录计算用)")
    @TableField(exist = false)
    private String measurementUnit;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    @TableField(value = "approver")
    @LogTag(name = "批准人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String approver;

    /**
     * 审核人名字
     */
    @ApiModelProperty("审核人名字")
    @TableField(exist = false)
    private String approverName;

    /**
     * 实际审批人
     */
    @ApiModelProperty("实际审批人")
    @TableField(value = "actual_approver")
    @LogTag(name = "审核人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String actualApprover;

    /**
     * 实际审批人签名地址
     */
    @ApiModelProperty("实际审批人签名地址")
    @TableField(exist = false)
    private String actualApproverSignatureUrl;

    /**
     * 实际审批人名字
     */
    @ApiModelProperty("实际审批人名字")
    @TableField(exist = false)
    private String actualApproverName;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    @TableField(value = "approval_status")
    @LogTag(name = "审批状态")
    private Integer approvalStatus;

    /**
     * 审批状态名称
     */
    @ApiModelProperty("审批状态名称")
    @TableField(exist = false)
    private String approvalStatusName;

    /**
     * 审批建议
     */
    @ApiModelProperty("审批建议")
    @TableField(value = "approval_suggestion")
    @LogTag(name = "审批建议")
    private String approvalSuggestion;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    @TableField(value = "approval_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;

    /**
     * 规则id（批量创建工单）
     */
//    @TableField(exist = false)
//    private Integer numberRuleId;

    /**
     * 生产时间段
     * <p>
     * [
     * {"startDate": "2021-11-11 13:28:33:00","endDate": "2021-11-11 13:33:04:00"}
     * {"startDate": "2021-11-11 13:28:33:00","endDate": "2021-11-11 13:33:04:00"}
     * {"startDate": "2021-11-11 13:28:33:00","endDate": "2021-11-11 13:33:04:00"}
     * ]
     */
    @ApiModelProperty("生产时间段")
    @TableField(exist = false)
    private List<Map> produceRange;

    /**
     * 物料iD
     */
    @ApiModelProperty("物料iD")
    @TableField(exist = false)
    private Integer materialId;
    /**
     * 工艺Id
     */
    @ApiModelProperty("工艺Id")
    @TableField(value = "craft_id")
    private Integer craftId;

    /**
     * 工艺编号
     */
    @ApiModelProperty("工艺编号")
    @TableField(value = "craft_code")
    private String craftCode;

    /**
     * 工艺名称
     */
    @ApiModelProperty("工艺名称")
    @TableField(exist = false)
    private String craftName;
    /**
     * 附件地址列表
     */
    @ApiModelProperty("附件地址列表")
    @TableField(exist = false)
    private List<WorkOrderFileEntity> file;
    /**
     * 产前状态
     */
    @ApiModelProperty("产前状态")
    @TableField(value = "prenatal_status")
    private Boolean prenatalStatus;

    /**
     * 计划工时
     */
    @ApiModelProperty("计划工时")
    @TableField(value = "planned_working_hours")
    private Double plannedWorkingHours;

    /**
     * 实际工时
     */
    @ApiModelProperty("实际工时")
    @TableField(value = "actual_working_hours")
    private Double actualWorkingHours;
    /**
     * 工单执行
     */
    @ApiModelProperty("工单执行")
    @TableField(value = "execution_status")
    private String executionStatus;
    /**
     * 工单执行
     */
    @ApiModelProperty("工单执行")
    @TableField(exist = false)
    private String executionStatusName;
    /**
     * 时差
     */
    @ApiModelProperty("时差")
    @TableField(value = "time_difference")
    private Double timeDifference;

    /**
     * 调机时长
     */
    @ApiModelProperty("调机时长")
    @TableField(exist = false)
    private String workOrderRecordTime;

    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    @TableField(value = "product_count")
    @UnitFormatColumn
    private Double productCount;

    /**
     * 待排数量
     */
    @ApiModelProperty("待排数量")
    @TableField(value = "pendent_quantity")
    @UnitFormatColumn
    private Double pendentQuantity;

    /**
     * 流转数量
     */
    @ApiModelProperty("流转数量")
    @TableField(value = "in_stock_count")
    @UnitFormatColumn
    private Double inStockCount;

    /**
     * 工作中心ID
     */
    @ApiModelProperty("工作中心ID")
    @TableField(value = "work_center_id")
    private Integer workCenterId;

    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    @TableField(value = "work_center_name")
    private String workCenterName;

    /**
     * 工作中心type
     */
    @TableField(value = "work_center_type")
    private String workCenterType;


    /**
     * 基本生产单元名称
     */
    @ApiModelProperty(value = "生产基本单元编码,多个按逗号分隔,推荐查询productBasicUnits字段")
    @TableField(exist = false)
    @Deprecated
    private String productionBasicUnitCode;

    /**
     * 基本生产单元名称
     */
    @ApiModelProperty("生产基本单元名称,多个按逗号分隔,推荐查询productBasicUnits字段")
    @TableField(exist = false)
    @Deprecated
    private String productionBasicUnitName;

    /**
     * 入库数量（先保留一段时间，避免业务存在问题，后续待仓库kafka消息推送后再删除，使用inventoryQuantity字段）
     */
    @Deprecated
    @ApiModelProperty("入库数量")
    @TableField(exist = false)
    private Double inputCount;

    /**
     * 已领料数
     */
    @ApiModelProperty("已领料数")
    @TableField(value = "picking_quantity")
    @UnitFormatColumn
    private Double pickingQuantity;

    /**
     * 已入库数
     */
    @ApiModelProperty("已入库数")
    @TableField(value = "inventory_quantity")
    @UnitFormatColumn
    private Double inventoryQuantity;

    /**
     * 供应商code
     */
    @ApiModelProperty("供应商code")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 供应商名字
     */
    @ApiModelProperty("供应商名字")
    @TableField(exist = false)
    private String supplierName;

    /**
     * 是否正在计数
     */
    @ApiModelProperty("是否正在计数")
    @TableField(exist = false)
    @Builder.Default
    private Boolean isCountNow = false;
    /**
     * 附件列表
     */
    @ApiModelProperty("附件列表")
    @TableField(exist = false)
    private List<AppendixEntity> appendixEntities;

    /**
     * 工序id
     */
    @ApiModelProperty("工序id")
    @TableField(exist = false)
    private String procedureIds;

    /**
     * 工序名称(用于大屏展示)
     */
    @ApiModelProperty("工序名称(用于大屏展示)")
    @TableField(exist = false)
    private String procedureName;
    @ApiModelProperty("工序别名")
    @TableField(exist = false)
    private String procedureAlias;

    /**
     * 有效工时=报工数量/标准产能
     */
    @TableField(value = "effective_working_hour")
    private Double effectiveWorkingHour;

    /**
     * 计数器累计参考值
     */
    @TableField(value = "auto_count")
    private Double autoCount;

    /**
     * 作业工单列表
     */
    @ApiModelProperty("作业工单列表")
    @TableField(exist = false)
    private List<OperationOrderDTO> operationOrderDTOS;

    /**
     * 绑定的工艺工序列表
     */
    @ApiModelProperty("绑定的工艺工序列表")
    @TableField(exist = false)
    private List<CraftProcedureEntity> craftProcedureEntities;

    /**
     * 流转时长
     */
    @ApiModelProperty("流转时长")
    @TableField(value = "circulation_duration")
    private Double circulationDuration;

    /**
     * 数据导入时间
     */
    @ApiModelProperty("数据导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "import_time")
    private Date importTime;

    /**
     * 第一次投入时是否更改了投产时间
     */
    @TableField(exist = false)
    private Boolean isChangeInvestTime;

    /**
     * 旧投产时间
     */
    @TableField(exist = false)
    private Date oldInvestTime;

    /**
     * 物料库存数量
     */
    @ApiModelProperty("物料库存数量")
    @TableField(exist = false)
    private Double stockQuantity;

    /**
     * 状态变更时间
     */
    @ApiModelProperty("状态变更时间")
    @TableField(value = "state_change_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stateChangeTime;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    @TableField(exist = false)
    private MaterialEntity materialFields;

    /**
     * 排产状态(0-不可排,1-待排产,2-已排产)
     */
    @ApiModelProperty("排产状态(0-不可排,1-待排产,2-已排产)")
    @TableField(value = "plan_state")
    private Integer planState;

    /**
     * 排程顺序
     */
    @ApiModelProperty("排程顺序")
    @TableField(value = "scheduling_sequence")
    private Integer schedulingSequence;

    /**
     * 排程状态
     */
    @ApiModelProperty("排程状态")
    @TableField(value = "scheduling_state")
    private Integer schedulingState;

    /**
     * 排程状态名称
     */
    @TableField(exist = false)
    @ApiModelProperty("排程状态名称")
    private String schedulingStateName;

    /**
     * 排程数量
     */
    @ApiModelProperty("排程数量")
    @TableField(value = "scheduling_count")
    private Double schedulingCount;

    @ApiModelProperty("流转状态(0-不可流转,1-可流转,2-流转超时,3-已流转)")
    @TableField(value = "circulation_state")
    private Integer circulationState;

    /**
     * 班组ID
     */
    @ApiModelProperty("班组ID")
    @TableField(value = "team_id")
    private Integer teamId;

    /**
     * 设备ID
     */
    @ApiModelProperty("设备ID")
    @TableField(value = "device_id")
    private Integer deviceId;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    @TableField(exist = false)
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    @TableField(exist = false)
    private String deviceName;

    /**
     * 生产基本单元id
     */
    @TableField(value = "production_basic_unit_id")
    private Integer productionBasicUnitId;

    /**
     * 隔离ID(工作中心ID+“-”+生产基本单元id)
     */
    @TableField(value = "isolation_id")
    private String isolationId;

    /**
     * 客户物料编码
     */
    @ApiModelProperty("客户物料编码")
    @TableField(value = "customer_material_code")
    private String customerMaterialCode;

    /**
     * 客户物料名称
     */
    @ApiModelProperty("客户物料名称")
    @TableField(value = "customer_material_name")
    private String customerMaterialName;

    /**
     * 客户物料规格
     */
    @ApiModelProperty("客户物料规格")
    @TableField(value = "customer_specification")
    private String customerSpecification;

    /**
     * 班组名称
     */
    @ApiModelProperty("班组名称")
    @TableField(exist = false)
    private String teamName;

    /**
     * 班组长真实姓名
     */
    @ApiModelProperty("班组长真实姓名")
    @TableField(exist = false)
    private String teamLeaderNickName;

    /**
     * 班组组员名称
     */
    @ApiModelProperty("班组组员名称")
    @TableField(exist = false)
    private String teamMemberNickNames;

    /**
     * 工单投产班组成员列表
     */
    @ApiModelProperty("工单投产班组成员列表")
    @TableField(exist = false)
    private List<WorkOrderTeamEntity> workOrderTeamEntities;

    /**
     * 工单事件(大屏的工单备注信息要用)
     */
    @TableField(exist = false)
    private String workOrderEvent;

    @TableField(exist = false)
    private String planStateName;

    @TableField(exist = false)
    private String circulationStateName;

    /**
     * 工作中心编码
     */
    @TableField(exist = false)
    private String workCenterCode;


    /**
     * 工作中心类型名称
     */
    @TableField(exist = false)
    private String workCenterTypeName;

    /**
     * 工作中心关联资源type
     */
    @TableField(exist = false)
    private String workCenterRelevanceType;

    /**
     * 工单关联班组id
     */
    @TableField(exist = false)
    private List<Integer> relevanceTeamIds;

    /**
     * 工单关联班组名称
     */
    @TableField(exist = false)
    private List<String> relevanceTeamNames;

    /**
     * 工单关联设备id
     */
    @TableField(exist = false)
    private List<Integer> relevanceDeviceIds;
    /**
     * 工单关联设备名称
     */
    @TableField(exist = false)
    private List<String> relevanceDeviceNames;

    /**
     * 工单关联制造单元id
     */
    @TableField(exist = false)
    private List<Integer> relevanceLineIds;
    /**
     * 工单关联制造单元名称
     */
    @TableField(exist = false)
    private List<String> relevanceLineNames;

    @ApiModelProperty(value = "关联资源列表")
    @TableField(exist = false)
    private List<WorkOrderRelevanceVO> workOrderRelevanceVOS;

    /**
     * 合格率= 完成数/（不良数+完成数）
     */
    @TableField(exist = false)
    private Double passRate;

    /**
     * 工艺工序id
     */
    @TableField(exist = false)
    private Integer craftProcedureId;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    @TableField(value = "sku_id")
    private Integer skuId;

    /**
     * 包装方案编码
     */
    @ApiModelProperty("包装方案编码")
    @TableField(value = "package_scheme_code")
    private String packageSchemeCode;

    /**
     * 包装方案列表
     */
    @ApiModelProperty("包装方案列表")
    @TableField(exist = false)
    private List<PackageSchemeEntity> packageSchemeEntities;

    /**
     * 关联的销售订单物料行号
     */
    @ApiModelProperty("关联的销售订单物料行号")
    @TableField(exist = false)
    private Integer relatedSaleOrderMaterialLineNumber;

    /**
     * 关联的生产订单物料行号
     */
    @ApiModelProperty("关联的生产订单物料行号")
    @TableField(exist = false)
    private Integer relatedProductOrderMaterialLineNumber;

    /**
     * 生产订单按工艺路线下推次数
     */
    @ApiModelProperty("生产订单按工艺路线下推次数")
    @TableField(value = "push_times")
    private Integer pushTimes;

    /**
     * 是否已打印
     */
    @ApiModelProperty("是否已打印")
    @TableField(value = "is_print")
    private Boolean isPrint;

    /**
     * 领料状态名称
     */
    @ApiModelProperty("领料状态名称")
    @TableField(value = "picking_state_name")
    private String pickingStateName;

    /**
     * 工单分日计划
     */
    @TableField(exist = false)
    @UnitFormatContainer
    private List<WorkOrderPlanEntity> workOrderPlanList;

    /**
     * 销售订单
     */
    @TableField(exist = false)
    private SaleOrderVO saleOrderVO;

    /**
     * 生产订单
     */
    @TableField(exist = false)
    private ProductOrderEntity productOrderEntity;

    /**
     * 关联的批次计划总数
     */
    @TableField(exist = false)
    private Double relatedBarCodePlanSum;

    /**
     * 是否通过对外接口插入/更新
     * 某些字段接口需要提供更新，但是不能影响系统原本的业务
     */
    @TableField(exist = false)
    private Boolean operateByApi = false;

    /**
     * 上料防错类型
     */
    @ApiModelProperty(value = "上料防错类型")
    @TableField(value = "material_check_type")
    private String materialCheckType;

    /**
     * 上料防错类型名称
     */
    @ApiModelProperty(value = "上料防错类型名称")
    @TableField(exist = false)
    private String materialCheckTypeName;

    /**
     * 投产结果明细
     */
    @ApiModelProperty(value = "投产结果明细")
    @TableField(exist = false)
    private String investCheckResultDetail;

    /**
     * 上料防错是否支持替代料，0否1是
     */
    @ApiModelProperty("上料防错是否支持替代料")
    @TableField(value = "material_check_replace")
    private Boolean materialCheckReplace;

    /**
     * 关联的上料防错物料列表
     */
    @TableField(exist = false)
    private List<WorkOrderMaterialCheckMaterialEntity> checkMaterialList;

    /**
     * 使用到的编码规则id
     *
     * @param
     * @return
     */
    @TableField(exist = false)
    private Integer numberRuleId;

    /**
     * 工单扩展字段1
     */
    @ApiModelProperty(value = "工单扩展字段1")
    @TableField(value = "work_order_extend_field_one")
    @LogTag(name = "工单扩展字段1")
    private String workOrderExtendFieldOne;
    /**
     * 工单扩展字段2
     */
    @ApiModelProperty(value = "工单扩展字段2")
    @TableField(value = "work_order_extend_field_two")
    @LogTag(name = "工单扩展字段2")
    private String workOrderExtendFieldTwo;
    /**
     * 工单扩展字段3
     */
    @ApiModelProperty(value = "工单扩展字段3")
    @TableField(value = "work_order_extend_field_three")
    @LogTag(name = "工单扩展字段3")
    private String workOrderExtendFieldThree;
    /**
     * 工单扩展字段4
     */
    @ApiModelProperty(value = "工单扩展字段4")
    @TableField(value = "work_order_extend_field_four")
    @LogTag(name = "工单扩展字段4")
    private String workOrderExtendFieldFour;
    /**
     * 工单扩展字段5
     */
    @ApiModelProperty(value = "工单扩展字段5")
    @TableField(value = "work_order_extend_field_five")
    @LogTag(name = "工单扩展字段5")
    private String workOrderExtendFieldFive;

    /**
     * 工单扩展字段6
     */
    @ApiModelProperty(value = "工单扩展字段6")
    @TableField(value = "work_order_extend_field_six")
    @LogTag(name = "工单扩展字段6")
    private String workOrderExtendFieldSix;
    /**
     * 工单扩展字段7
     */
    @ApiModelProperty(value = "工单扩展字段7")
    @TableField(value = "work_order_extend_field_seven")
    @LogTag(name = "工单扩展字段7")
    private String workOrderExtendFieldSeven;
    /**
     * 工单扩展字段8
     */
    @ApiModelProperty(value = "工单扩展字段8")
    @TableField(value = "work_order_extend_field_eight")
    @LogTag(name = "工单扩展字段8")
    private String workOrderExtendFieldEight;
    /**
     * 工单扩展字段9
     */
    @ApiModelProperty(value = "工单扩展字段9")
    @TableField(value = "work_order_extend_field_nine")
    @LogTag(name = "工单扩展字段9")
    private String workOrderExtendFieldNine;
    /**
     * 工单扩展字段10
     */
    @ApiModelProperty(value = "工单扩展字段10")
    @TableField(value = "work_order_extend_field_ten")
    @LogTag(name = "工单扩展字段10")
    private String workOrderExtendFieldTen;
    /**
     * 工单物料扩展字段1
     */
    @ApiModelProperty(value = "工单物料扩展字段1")
    @LogTag(name = "工单物料扩展字段1")
    @TableField(value = "work_order_material_extend_field_one")
    private String workOrderMaterialExtendFieldOne;
    /**
     * 工单物料扩展字段2
     */
    @ApiModelProperty(value = "工单物料扩展字段2")
    @LogTag(name = "工单物料扩展字段2")
    @TableField(value = "work_order_material_extend_field_two")
    private String workOrderMaterialExtendFieldTwo;
    /**
     * 工单物料扩展字段3
     */
    @ApiModelProperty(value = "工单物料扩展字段3")
    @LogTag(name = "工单物料扩展字段3")
    @TableField(value = "work_order_material_extend_field_three")
    private String workOrderMaterialExtendFieldThree;
    /**
     * 工单物料扩展字段4
     */
    @ApiModelProperty(value = "工单物料扩展字段4")
    @LogTag(name = "工单物料扩展字段4")
    @TableField(value = "work_order_material_extend_field_four")
    private String workOrderMaterialExtendFieldFour;
    /**
     * 工单物料扩展字段5
     */
    @ApiModelProperty(value = "工单物料扩展字段5")
    @LogTag(name = "工单物料扩展字段5")
    @TableField(value = "work_order_material_extend_field_five")
    private String workOrderMaterialExtendFieldFive;
    /**
     * 工单物料扩展字段6
     */
    @ApiModelProperty(value = "工单物料扩展字段6")
    @LogTag(name = "工单物料扩展字段6")
    @TableField(value = "work_order_material_extend_field_six")
    private String workOrderMaterialExtendFieldSix;
    /**
     * 工单物料扩展字段7
     */
    @ApiModelProperty(value = "工单物料扩展字段7")
    @LogTag(name = "工单物料扩展字段7")
    @TableField(value = "work_order_material_extend_field_seven")
    private String workOrderMaterialExtendFieldSeven;
    /**
     * 工单物料扩展字段8
     */
    @ApiModelProperty(value = "工单物料扩展字段8")
    @LogTag(name = "工单物料扩展字段8")
    @TableField(value = "work_order_material_extend_field_eight")
    private String workOrderMaterialExtendFieldEight;
    /**
     * 工单物料扩展字段9
     */
    @ApiModelProperty(value = "工单物料扩展字段9")
    @LogTag(name = "工单物料扩展字段9")
    @TableField(value = "work_order_material_extend_field_nine")
    private String workOrderMaterialExtendFieldNine;
    /**
     * 工单物料扩展字段10
     */
    @ApiModelProperty(value = "工单物料扩展字段10")
    @LogTag(name = "工单物料扩展字段10")
    @TableField(value = "work_order_material_extend_field_ten")
    private String workOrderMaterialExtendFieldTen;

    /**
     * 投产检查结果（true--通过  false--不通过  null--空）
     */
    @ApiModelProperty(value = "投产检查结果（true--通过  false--不通过  null--空）")
    @TableField(value = "invest_check_result")
    private Boolean investCheckResult;


    /**
     * 业务主体编码
     */
    @ApiModelProperty("业务主体编码")
    @TableField(value = "business_unit_code")
    private String businessUnitCode;
    /**
     * 业务主体名称
     */
    @ApiModelProperty("业务主体名称")
    @TableField(value = "business_unit_name")
    private String businessUnitName;

    /**
     * 投产检查结果名称
     */
    @ApiModelProperty(value = "投产检查结果名称")
    @TableField(exist = false)
    private String investCheckResultName;

    /**
     * 维修数量
     */
    @ApiModelProperty("维修数量")
    @TableField(exist = false)
    private Integer maintainCount;

    /**
     * 是否允许自动新增报工记录
     * 条件：符合`完工自动报工`的配置条件下，完工时检查有无报工记录，如无报工记录，自动补充等于计划数的报工记录
     */
    @ApiModelProperty("是否允许自动新增报工记录")
    @TableField(exist = false)
    private Boolean isAutoReport;

    /**
     * 扩展字段中文名
     */
    @TableField(exist = false)
    private String workOrderExtendFieldOneName;
    @TableField(exist = false)
    private String workOrderExtendFieldTwoName;
    @TableField(exist = false)
    private String workOrderExtendFieldThreeName;
    @TableField(exist = false)
    private String workOrderExtendFieldFourName;
    @TableField(exist = false)
    private String workOrderExtendFieldFiveName;
    @TableField(exist = false)
    private String workOrderExtendFieldSixName;
    @TableField(exist = false)
    private String workOrderExtendFieldSevenName;
    @TableField(exist = false)
    private String workOrderExtendFieldEightName;
    @TableField(exist = false)
    private String workOrderExtendFieldNineName;
    @TableField(exist = false)
    private String workOrderExtendFieldTenName;
    @TableField(exist = false)
    private String workOrderMaterialExtendFieldOneName;
    @TableField(exist = false)
    private String workOrderMaterialExtendFieldTwoName;
    @TableField(exist = false)
    private String workOrderMaterialExtendFieldThreeName;
    @TableField(exist = false)
    private String workOrderMaterialExtendFieldFourName;
    @TableField(exist = false)
    private String workOrderMaterialExtendFieldFiveName;
    @TableField(exist = false)
    private String workOrderMaterialExtendFieldSixName;
    @TableField(exist = false)
    private String workOrderMaterialExtendFieldSevenName;
    @TableField(exist = false)
    private String workOrderMaterialExtendFieldEightName;
    @TableField(exist = false)
    private String workOrderMaterialExtendFieldNineName;
    @TableField(exist = false)
    private String workOrderMaterialExtendFieldTenName;

    @Override
    public Serializable pkVal() {
        return this.workOrderId;
    }

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date fakerTime;

    /**
     * 单件理论工时
     */
    @TableField(exist = false)
    private Double theoryHour;
    /**
     * 产出理论工时
     */
    @TableField(exist = false)
    private Double produceTheoryHour;
    /**
     * 计划理论工时
     */
    @TableField(exist = false)
    private Double planTheoryHour;

    /**
     * 产能单位名称
     */
    @TableField(exist = false)
    private String capacityUnitName;

    /**
     * 是否委外
     */
    @TableField(exist = false)
    private Boolean isSubcontract;

    /**
     * 原工单号
     */
    @ApiModelProperty("原工单号")
    @TableField(value = "original_work_order_number")
    private String originalWorkOrderNumber;

    @TableField(exist = false)
    private List<WorkOrderSubcontractEntity> workOrderSubcontracts;

    /**
     * 工单工艺信息
     * 工艺、默认工艺、无工艺
     */
    @TableField(exist = false)
    private String haveCraft;

    /**
     * 直通数
     */
    @TableField(exist = false)
    private Double directAccessQuantity;
    /**
     * 直通率
     */
    @TableField(exist = false)
    private Double directAccessRate;

    /**
     * 是否提交审批，默认否
     */
    @TableField(exist = false)
    private Boolean isSubmitApprove;

    /**
     * 外部审批编号
     */
    @ApiModelProperty("外部审批编号")
    @TableField("outer_approve_number")
    private String outerApproveNumber;

    @ApiModelProperty("绑定的生产基本单元列表")
    @TableField(exist = false)
    private List<WorkOrderBasicUnitRelationEntity> productBasicUnits;

    @ApiModelProperty("生产基本单元投产记录")
    @TableField(exist = false)
    private List<WorkOrderBasicUnitInputRecordEntity> productBasicUnitInputRecords;

    /**
     * 报工计数
     */
    @TableField(exist = false)
    private Long reportLineCount;
    /**
     * 报工计数
     */
    @TableField(exist = false)
    private Long reportLineBarCodeCount;
    /**
     * 批次计数
     */
    @TableField(exist = false)
    private Long barCodeCount;

    /**
     * 下推数量
     */
    @TableField(exist = false)
    private Double pushDownQuantity;

    @TableField(exist = false)
    private Boolean autoCreatePlan;


    /**
     * 为兼容旧版本，如果传lineId而不传productBasicUnits，则使用lineId作为productBasicUnits
     * 设备、班组同理
     */
    public List<WorkOrderBasicUnitRelationEntity> getProductBasicUnits() {
        if (CollUtil.isEmpty(productBasicUnits) && Objects.nonNull(lineId)) {
            productBasicUnits = Collections.singletonList(WorkOrderBasicUnitRelationEntity.builder()
                    .workOrderNumber(workOrderNumber)
                    .workCenterId(workCenterId)
                    .workCenterType(WorkCenterTypeEnum.LINE.getCode())
                    .productionBasicUnitId(lineId)
                    .isolationId(workCenterId + Constants.CROSSBAR + lineId)
                    .build());
        } else if (CollUtil.isEmpty(productBasicUnits) && Objects.nonNull(deviceId)) {
            productBasicUnits = Collections.singletonList(WorkOrderBasicUnitRelationEntity.builder()
                    .workOrderNumber(workOrderNumber)
                    .workCenterId(workCenterId)
                    .workCenterType(WorkCenterTypeEnum.DEVICE.getCode())
                    .productionBasicUnitId(deviceId)
                    .isolationId(workCenterId + Constants.CROSSBAR + deviceId)
                    .build());
        } else if (CollUtil.isEmpty(productBasicUnits) && Objects.nonNull(teamId)) {
            productBasicUnits = Collections.singletonList(WorkOrderBasicUnitRelationEntity.builder()
                    .workOrderNumber(workOrderNumber)
                    .workCenterId(workCenterId)
                    .workCenterType(WorkCenterTypeEnum.TEAM.getCode())
                    .productionBasicUnitId(teamId)
                    .isolationId(workCenterId + Constants.CROSSBAR + teamId)
                    .build());
        }
        return productBasicUnits;
    }

    public String getProductBasicUnitsIdStr() {
        if (CollUtil.isEmpty(productBasicUnits)) {
            return null;
        }
        return productBasicUnits.stream().filter(e -> e.getProductionBasicUnitId() != null).map(e -> e.getProductionBasicUnitId().toString()).collect(Collectors.joining(","));
    }

    public String getProductBasicUnitsNameStr() {
        if (CollUtil.isEmpty(productBasicUnits)) {
            return null;
        }
        return productBasicUnits.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitName).filter(Objects::nonNull).collect(Collectors.joining(","));
    }

    public String getTeamsIdStr() {
        return WorkCenterTypeEnum.TEAM.getCode().equals(this.workCenterType) ? getProductBasicUnitsIdStr() : null;
    }

    public String getTeamsNameStr() {
        return WorkCenterTypeEnum.TEAM.getCode().equals(this.workCenterType) ? getProductBasicUnitsNameStr() : null;
    }

    public String getDevicesIdStr() {
        return WorkCenterTypeEnum.DEVICE.getCode().equals(this.workCenterType) ? getProductBasicUnitsIdStr() : null;
    }

    public String getDevicesNameStr() {
        return WorkCenterTypeEnum.DEVICE.getCode().equals(this.workCenterType) ? getProductBasicUnitsNameStr() : null;
    }

    /**
     * 下推标识信息列表
     */
    @ApiModelProperty("下推标识信息列表")
    @TableField(exist = false)
    private List<PushDownIdentifierInfoDTO> pushDownIdentifierInfos;


    public interface Insert {
    }

    public interface Update {
    }

    @Override
    public PushDownOrderStateEnum calPushDownOrderState() {
        if (state == null) {
            return null;
        }
        WorkOrderStateEnum stateEnum = WorkOrderStateEnum.getByCode(state);
        switch (stateEnum) {
            case RELEASED:
                return PushDownOrderStateEnum.RELEASED;
            case FINISHED:
                return PushDownOrderStateEnum.FINISHED;
            case CLOSED:
                return PushDownOrderStateEnum.CLOSED;
            case CANCELED:
                return PushDownOrderStateEnum.CANCELED;
        }
        return null;
    }
}
