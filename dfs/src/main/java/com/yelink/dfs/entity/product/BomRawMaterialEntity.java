package com.yelink.dfs.entity.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.validate.FieldValueValidate;
import com.yelink.dfscommon.entity.dfs.RationalDTO;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.Rational;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-04-10 13:16
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@LogTag(bean = "bomRawMaterialServiceImpl", method = "getById", param = "id")
@TableName("dfs_bom_raw_material")
public class BomRawMaterialEntity extends Model<BomRawMaterialEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * bomId
     */
    @ApiModelProperty("bomId")
    @TableField(value = "bom_id")
    private Integer bomId;

    /**
     * 自己的bomId
     */
    @ApiModelProperty("子物料的bomId")
    @TableField(exist = false)
    private Integer relatedBomId;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @TableField(value = "code")
    @LogTag(isKey = true, name = "物料编码")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    @TableField(value = "bom_describe")
    private String bomDescribe;


    /**
     * 用量：分子
     */
    @ApiModelProperty("用量：分子")
    @TableField(value = "num")
    @LogTag(name = "用量：分子")
    private BigDecimal num;


    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value = "remark")
    @LogTag(name = "备注")
    private String remark;

    /**
     * 状态名称
     */
    @ApiModelProperty("状态名称")
    @TableField(exist = false)
    private Integer type;

    /**
     * 参考数量
     */
    @ApiModelProperty("参考数量")
    @TableField(exist = false)
    private Double referenceNum;

    /**
     * 计划数量
     */
    @ApiModelProperty("计划数量")
    @TableField(exist = false)
    private Double planNum;

    /**
     * 实际数量
     */
    @ApiModelProperty("实际数量")
    @TableField(exist = false)
    private Double actualNum;
    /**
     * 仓库总数量
     */
    @ApiModelProperty("仓库总数量")
    @TableField(exist = false)
    private Double allQuantity;

    /**
     * 物料Id
     */
    @ApiModelProperty("物料Id")
    @TableField(exist = false)
    private Integer materialId;

    /**
     * 领料申请数量
     */
    @ApiModelProperty("领料申请数量")
    @TableField(exist = false)
    private Double applyQuantity;
    /**
     * 领料数量
     */
    @ApiModelProperty("领料数量")
    @TableField(exist = false)
    private Double takeOutQuantity;
    /**
     * 退料数量
     */
    @ApiModelProperty("退料数量")
    @TableField(exist = false)
    private Double returnQuantity;
    /**
     * 当前领用数量
     */
    @ApiModelProperty("当前领用数量")
    @TableField(exist = false)
    private Double curUseQuantity;
    /**
     * 当前库存数量
     */
    @ApiModelProperty("当前库存数量")
    @TableField(exist = false)
    private Double stockQuantity;

    @ApiModelProperty("物料数量扣除库存后是否齐套")
    @TableField(exist = false)
    private Boolean isComplete;

    /**
     * 多级bom的子bom
     */
    @ApiModelProperty("多级bom的子bom")
    @TableField(exist = false)
    private List<BomRawMaterialEntity> children;

    /**
     * 生产工单多级bom对应的物料数量
     */
    @ApiModelProperty("生产工单多级bom对应的物料数量")
    @TableField(exist = false)
    private BigDecimal workOrderMaterialQuantity;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    @TableField(exist = false)
    private MaterialEntity materialFields;


    /**
     * bom子项类型
     */
    @ApiModelProperty("bom子项类型")
    @TableField(value = "bom_type")
    @LogTag(name = "bom子项类型")
    private String bomType;

    /**
     * bom子项类型名称
     */
    @TableField(exist = false)
    private String bomTypeName;

    /**
     * 工序编号
     */
    @ApiModelProperty("工序编号")
    @TableField(value = "procedure_code")
    @LogTag(name = "工序编号")
    private String procedureCode;

    /**
     * bom单号
     */
    @ApiModelProperty("bom单号")
    @TableField(value = "bom_num")
    @LogTag(name = "bom单号")
    private String bomNum;

    /**
     * bom版本
     */
    @ApiModelProperty("bom版本")
    @TableField(value = "version")
    @LogTag(name = "bom版本")
    private String version;

    /**
     * 固定损耗
     */
    @ApiModelProperty("固定损耗")
    @TableField(value = "fixed_damage")
    @LogTag(name = "固定损耗")
    private Double fixedDamage;

    /**
     * 预估损耗率
     */
    @ApiModelProperty("预估损耗率")
    @TableField(value = "loss_rate")
    @LogTag(name = "损耗率")
    private Double lossRate;

    /**
     * 用量：分母
     */
    @ApiModelProperty("用量：分母")
    @TableField(value = "number")
    @LogTag(name = "用量：分母")
    private BigDecimal number;

    /**
     * 用量 = 系数num/分母number
     */
    @ApiModelProperty("用量")
    @TableField(exist = false)
    private Double useNum;

    /**
     * 替代物id（多个则按逗号分隔）
     */
    @ApiModelProperty("替代物id（多个则按逗号分隔）")
    @TableField(exist = false)
    @FieldValueValidate(regex = "\\d+")
    private String replaceMaterialId;

    /**
     * 特征参数
     */
    @ApiModelProperty("特征参数")
    @TableField(exist = false)
    private SkuEntity skuEntity;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    @TableField(value = "sku_id")
    private Integer skuId;

    /**
     * 扩展字段1
     */
    @TableField(value = "extend_one")
    private Object extendOne;

    /**
     * 通过父BOM的系数与该层系数换算而来的新的系数
     */
    @TableField(exist = false)
    private RationalDTO rational;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    /**
     * 父BOM的分子分母和该对象的分子分母进行通分，获得新的分子分母,组装成rational
     */
    public void calRational(Rational rational) {
        Rational thisRational = new Rational(this.getNum().doubleValue(), this.getNumber().doubleValue());
        Rational mulitiply = rational.mulitiply(thisRational);
        this.rational = RationalDTO.builder().numerator(mulitiply.getNumerator()).denominator(mulitiply.getDenominator()).build();
    }

}
