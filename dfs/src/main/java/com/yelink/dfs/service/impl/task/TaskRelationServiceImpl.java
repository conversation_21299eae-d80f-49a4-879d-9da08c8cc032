package com.yelink.dfs.service.impl.task;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.entity.task.TaskEntity;
import com.yelink.dfs.entity.task.TaskRelationEntity;
import com.yelink.dfs.entity.task.dto.TaskRelationDeleteDTO;
import com.yelink.dfs.entity.task.dto.TaskRelationUpsertDTO;
import com.yelink.dfs.mapper.task.TaskRelationMapper;
import com.yelink.dfs.service.task.TaskRelationService;
import com.yelink.dfs.service.task.TaskService;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.utils.ValidateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TaskRelationServiceImpl extends ServiceImpl<TaskRelationMapper, TaskRelationEntity> implements TaskRelationService {
    @Lazy
    @Resource
    private TaskService taskService;
    @Override
    public void upsert(TaskRelationUpsertDTO dto) {
        // 格式化
        if(StrUtil.isBlank(dto.getRelateType()) && StrUtil.isNotBlank(dto.getApplicationId())) {
            dto.setRelateType(dto.getApplicationId());
        }
        // 校验
        ValidateUtil.valid(dto, null);
        if(StrUtil.isBlank(dto.getRelateType())) {
            throw new ResponseException("关联类型不能为空");
        }
        //
        TaskEntity task = taskService.lambdaQuery()
                .eq(TaskEntity::getOrderCategory, dto.getOrderCategory())
                .eq(TaskEntity::getOrderNumber, dto.getOrderNumber())
                .eq(Objects.nonNull(dto.getMaterialLineId()), TaskEntity::getMaterialLineId, dto.getMaterialLineId())
                .last("limit 1")
                .one();
        if(task == null) {
            log.warn("单据关联项注册时，未找到主单据。类型：{}， 单据号：{}", dto.getOrderCategory(), dto.getOrderNumber());
            return;
        }
        TaskRelationEntity oldRelation = this.lambdaQuery()
                .eq(TaskRelationEntity::getTaskId, task.getTaskId())
                .eq(TaskRelationEntity::getRelateType, dto.getRelateType())
                .eq(TaskRelationEntity::getRelateId, dto.getRelateId())
                .one();
        // 插入
        if(oldRelation == null) {
            TaskRelationEntity taskRelation = TaskRelationEntity.builder()
                    .taskId(task.getTaskId())
                    .relateType(dto.getRelateType())
                    .relateId(dto.getRelateId())
                    .username(dto.getUsername())
                    .createTime(new Date())
                    .build();
            this.save(taskRelation);
        }else {
            // 更新
            oldRelation.setUpdateTime(new Date());
            oldRelation.setUsername(dto.getUsername());
            this.updateById(oldRelation);
        }

    }

    @Override
    public void delete(TaskRelationDeleteDTO dto) {
        ValidateUtil.valid(dto, null);
        TaskEntity task = taskService.lambdaQuery()
                .eq(TaskEntity::getOrderCategory, dto.getOrderCategory())
                .eq(TaskEntity::getOrderNumber, dto.getOrderNumber())
                .eq(Objects.nonNull(dto.getMaterialLineId()), TaskEntity::getMaterialLineId, dto.getMaterialLineId())
                .last("limit 1")
                .one();
        if(task == null) {
            log.warn("单据关联项删除时，未找到主单据。类型：{}， 单据号：{}", dto.getOrderCategory(), dto.getOrderNumber());
            return;
        }
        this.lambdaUpdate()
                .eq(TaskRelationEntity::getTaskId, task.getTaskId())
                .eq(TaskRelationEntity::getRelateType, dto.getRelateType())
                .eq(TaskRelationEntity::getRelateId, dto.getRelateId())
                .remove();
    }
}

